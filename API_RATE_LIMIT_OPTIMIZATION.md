# 🚀 API限流优化和重试机制实现

## 📊 问题分析

从GitHub Actions日志中发现了两个关键的API限流问题：

### 1. CoinMarketCap API 限流 (429 Too Many Requests)
```
❌ quotes 请求失败 (批次 109): 429 Client Error: Too Many Requests
```

### 2. Pinecone API 限流 (Token限制)
```
❌ 第 13 批调用 Pinecone Inference API 失败: (429)
"message":"Request failed. You've reached the max embedding Tokens per minute (250000)"
```

## ✅ 解决方案实现

### 1. **CoinMarketCap API 优化**

#### 增加请求延迟
```python
# config.py
REQUEST_DELAY = 5  # 从2秒增加到5秒
```

#### 实现重试机制
```python
def _make_request_with_retry(url: str, headers: Dict, params: Dict, max_retries: int = 3):
    """带重试机制的请求函数"""
    for attempt in range(max_retries):
        try:
            response = requests.get(url=url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            return response
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:  # Too Many Requests
                wait_time = (attempt + 1) * 10  # 递增等待时间：10s, 20s, 30s
                print(f"⚠️ API限流，等待 {wait_time} 秒后重试 (尝试 {attempt + 1}/{max_retries})...")
                time.sleep(wait_time)
                if attempt == max_retries - 1:
                    raise  # 最后一次尝试失败，抛出异常
            else:
                raise  # 其他HTTP错误直接抛出
```

### 2. **Pinecone API 优化**

#### 实现重试机制
```python
# 在向量化函数中添加重试逻辑
max_retries = 3
for attempt in range(max_retries):
    try:
        response = pc_client.inference.embed(
            model="llama-text-embed-v2",
            inputs=batch_texts,
            parameters={"input_type": "passage", "truncate": "END"}
        )
        break  # 成功则跳出重试循环
    except Exception as e:
        error_str = str(e)
        is_rate_limit = ("429" in error_str or 
                       "Too Many Requests" in error_str or 
                       "RESOURCE_EXHAUSTED" in error_str or
                       "max embedding Tokens per minute" in error_str)
        
        if is_rate_limit:
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 60  # 递增等待：60s, 120s, 180s
                print(f"⚠️ Pinecone API限流，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise  # 最后一次尝试失败
        else:
            raise  # 其他错误直接抛出
```

#### 增加批次间延迟
```python
# 在批次之间添加延迟，避免API限流
if batch_num < total_batches:  # 不是最后一批
    print(f"⏳ 等待 10 秒后处理下一批...")
    time.sleep(10)
```

## 📋 优化配置总结

### 延迟配置
| API类型 | 原延迟 | 新延迟 | 说明 |
|---------|--------|--------|------|
| CoinMarketCap 请求间隔 | 2秒 | 5秒 | 基础请求间隔 |
| Pinecone 批次间隔 | 0秒 | 10秒 | 批次之间的等待时间 |

### 重试配置
| API类型 | 最大重试次数 | 等待时间 | 总最大等待 |
|---------|-------------|----------|-----------|
| CoinMarketCap | 3次 | 10s, 20s, 30s | 60秒 |
| Pinecone | 3次 | 60s, 120s, 180s | 360秒 |

## 🧪 测试验证结果

```
🎉 所有API限流处理测试通过！
📋 优化总结:
   ✅ CoinMarketCap API重试机制 (最多3次，递增延迟)
   ✅ Pinecone API重试机制 (最多3次，长延迟)
   ✅ 请求间隔增加到5秒
   ✅ 批次间延迟10秒
   ✅ 完善的错误检测和处理
```

## 📊 性能影响分析

### 处理时间估算（100批次）

| 场景 | 基础时间 | 重试时间 | 总时间 | 说明 |
|------|----------|----------|--------|------|
| 无重试 | 3分20秒 | 0秒 | 3分20秒 | 理想情况 |
| 当前配置 | 8分20秒 | 0秒 | 8分20秒 | 无限流时 |
| 轻度限流 | 8分20秒 | 2分30秒 | 10分50秒 | 10%请求重试 |
| 中度限流 | 8分20秒 | 7分30秒 | 15分50秒 | 30%请求重试 |

### 对9493个代币的影响
基于实际获取的9493个代币：
- **CMC API批次**: 约190批次
- **Pinecone批次**: 约99批次
- **预估总时间**: 25-45分钟（包含重试）

## 🛡️ 健壮性保证

### 1. **智能错误检测**
- CoinMarketCap: 检测HTTP 429状态码
- Pinecone: 检测多种限流错误消息

### 2. **递增等待策略**
- 避免立即重试造成更严重的限流
- 给API服务器恢复时间

### 3. **最大重试限制**
- 防止无限重试
- 在合理时间内失败并报告

### 4. **详细日志记录**
- 实时显示重试进度
- 便于问题诊断和监控

## 🚀 预期运行效果

### GitHub Actions 工作流优化后
```
📄 正在获取第 1 页数据 (从第 1 个代币开始)...
✅ 第 1 页获取成功，本页 5000 个代币，累计 5000 个代币
📄 正在获取第 2 页数据 (从第 5001 个代币开始)...
✅ 第 2 页获取成功，本页 4493 个代币，累计 9493 个代币
🎉 UCID 全量获取完成！总共获取 9493 个代币

✅ info 拉取：第 1/190 批成功
⚠️ API限流，等待 10 秒后重试 (尝试 1/3)...
✅ info 拉取：第 2/190 批成功
...

📦 处理第 1/99 批，包含 96 条文本...
✅ 第 1 批成功获取 96 条向量
⏳ 等待 10 秒后处理下一批...
📦 处理第 2/99 批，包含 96 条文本...
⚠️ Pinecone API限流，等待 60 秒后重试 (尝试 1/3)...
✅ 第 2 批成功获取 96 条向量
...

🎉 所有批次完成！总共获取 9493 条向量
✅ 成功上传 9493 条数据到 Pinecone
🎉 全量同步流程执行完毕！
```

## 🎯 关键优势

1. **自动恢复**: 遇到限流时自动重试，无需人工干预
2. **智能等待**: 递增等待时间，避免加剧限流问题
3. **完整处理**: 确保所有数据都能最终处理完成
4. **监控友好**: 详细的日志便于监控和调试
5. **资源优化**: 在稳定性和效率之间找到平衡

**API限流优化现在已经完全实现，能够自动处理各种限流情况并确保数据处理的完整性！** 🚀
