# Report AI - 智能代币挖掘引擎技术规格文档

## 项目概述
Report AI是一个现代化的代币分析和推荐平台，采用React + TypeScript + Tailwind CSS构建，具有精致的UI设计、流畅的动画效果和智能搜索功能。

## 技术栈
- **前端框架**: React 18 + TypeScript
- **样式系统**: Tailwind CSS v4.0
- **动画库**: Motion/React (原Framer Motion)
- **UI组件库**: Shadcn/ui
- **图标库**: Lucide React
- **后端**: Supabase (Edge Functions + Database)

## 整体布局架构

### 主容器结构
```tsx
<div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
  {/* 背景装饰层 */}
  <div className="absolute inset-0 overflow-hidden">
    {/* 三个模糊光圈装饰 */}
  </div>
  
  <div className="relative z-10">
    <Header />
    <AnimatePresence mode="wait">
      {/* SearchSection 或 SearchResults */}
    </AnimatePresence>
  </div>
</div>
```

### 背景设计系统
**主背景**: 对角线渐变 `bg-gradient-to-br from-indigo-50 via-white to-purple-50`

**装饰性光圈**:
1. **右上角光圈**: 
   - 位置: `absolute -top-40 -right-40`
   - 尺寸: `w-80 h-80`
   - 渐变: `bg-gradient-to-br from-purple-400/20 to-indigo-600/20`
   - 效果: `rounded-full blur-3xl`

2. **左下角光圈**:
   - 位置: `absolute -bottom-40 -left-40`
   - 尺寸: `w-80 h-80`
   - 渐变: `bg-gradient-to-tr from-blue-400/20 to-purple-600/20`
   - 效果: `rounded-full blur-3xl`

3. **中心光圈**:
   - 位置: `absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2`
   - 尺寸: `w-96 h-96`
   - 渐变: `bg-gradient-to-r from-indigo-300/10 to-purple-300/10`
   - 效果: `rounded-full blur-3xl`

## Header组件详细规格

### 结构布局
```tsx
<header className="w-full px-6 py-4">
  <div className="max-w-7xl mx-auto flex items-center justify-between">
    {/* Logo区域 */}
    {/* 登录按钮 */}
  </div>
</header>
```

### Logo区域设计
- **容器**: `flex items-center gap-3`
- **图标容器**: 
  - 尺寸: `w-12 h-12`
  - 背景: `bg-gradient-to-br from-indigo-500 to-purple-600`
  - 圆角: `rounded-2xl`
  - 阴影: `shadow-lg`
  - 图标: Settings (6x6, 白色)
- **文字区域**:
  - 主标题: "Report AI" (text-xl, font-semibold, 渐变文字)
  - 副标题: "智能代币挖掘引擎" (text-sm, text-gray-500)

### 登录按钮设计
- **背景**: `bg-gradient-to-r from-indigo-500 to-purple-600`
- **悬停效果**: `hover:from-indigo-600 hover:to-purple-700`
- **内边距**: `px-6 py-2`
- **圆角**: `rounded-xl`
- **阴影**: `shadow-lg hover:shadow-xl`
- **过渡**: `transition-all duration-300`
- **图标**: LogIn (4x4, 右间距2)

## SearchSection组件详细规格

### 整体布局
```tsx
<div className="flex flex-col items-center justify-center min-h-[80vh] px-6">
  <div className="max-w-4xl mx-auto text-center space-y-8">
    {/* 主标题区域 */}
    {/* 搜索框区域 */}
    {/* 标签按钮区域 */}
    {/* 装饰性图标区域 */}
  </div>
</div>
```

### 主标题区域动画
- **动画**: `initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }}`
- **延迟**: `transition={{ duration: 0.8, delay: 0.2 }}`
- **Sparkles图标**: `w-8 h-8 text-purple-500`
- **主标题**: 
  - 字体: `text-5xl md:text-6xl font-bold`
  - 渐变: `bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent`
  - 行高: `leading-tight`
- **副标题**: `text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed`

### 搜索框设计
- **容器**: `relative max-w-2xl mx-auto`
- **输入框**:
  - 尺寸: `w-full h-16`
  - 内边距: `pl-6 pr-32`
  - 字体: `text-lg`
  - 背景: `bg-white/80 backdrop-blur-sm`
  - 边框: `border-2 border-gray-200/50`
  - 圆角: `rounded-2xl`
  - 阴影: `shadow-lg hover:shadow-xl focus:shadow-xl`
  - 焦点边框: `focus:border-purple-300`
- **搜索按钮**:
  - 位置: `absolute right-2 top-2`
  - 尺寸: `h-12 px-6`
  - 背景: `bg-gradient-to-r from-indigo-500 to-purple-600`
  - 文字: "挖掘"

### 标签按钮系统
**标签列表**: ['有叙事背景', '实力团队', '技术创新', '热门领域', '代币启动公平', '代币分配公平', '代币通缩', '有清晰发展路径', '社区质量高']

**单个标签样式**:
- 背景: `bg-white/60 backdrop-blur-sm`
- 边框: `border-2 border-gray-200/50`
- 悬停: `hover:border-purple-300 hover:bg-purple-50/80`
- 圆角: `rounded-xl`
- 内边距: `px-6 py-3`
- 阴影: `shadow-sm hover:shadow-md`

**动画序列**:
- 每个标签独立动画: `delay: 0.8 + index * 0.1`
- 缩放效果: `initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }}`

### 底部装饰区域
- **整体容器**: `mt-16`
- **布局**: `flex items-center justify-center gap-8 opacity-30`
- **左侧文字**: "自主挖掘优质代币"
  - 样式: `text-lg font-medium bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent`
- **中心图标**:
  - 容器: `w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full`
  - 图标: Search (12x12, 白色)
- **右侧文字**: "全市场覆盖" (样式同左侧)

## SearchResults组件详细规格

### 页面布局结构
```tsx
<div className="min-h-screen px-6 pt-8">
  <div className="max-w-7xl mx-auto space-y-8">
    {/* 搜索区域 */}
    {/* 搜索结果卡片 */}
    {/* 返回按钮 */}
    {/* 研究报告弹窗 */}
  </div>
</div>
```

### 搜索区域重复设计
- 保持与SearchSection相同的标题和搜索框设计
- 搜索框为只读状态，显示当前查询
- 标签按钮中当前选中的标签高亮显示

### 结果卡片设计
**外层容器**:
- 背景: `bg-white/80 backdrop-blur-sm`
- 边框: `border-2 border-gray-200/50`
- 阴影: `shadow-xl`
- 圆角: `rounded-3xl`
- 溢出: `overflow-hidden`

**结果头部**:
- 背景: `bg-gradient-to-r from-indigo-50 to-purple-50`
- 边框: `border-b border-gray-200/50`
- 内边距: `px-8 py-6`
- **左侧信息**:
  - 图标容器: `w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl`
  - 图标: TrendingUp (5x5, 白色)
  - 标题: "为您推荐 X 个代币" (text-xl, font-semibold)
  - 副标题: "基于'查询词'的智能匹配结果" (text-sm, text-gray-600)
- **右侧徽章**:
  - 背景: `bg-gradient-to-r from-green-500 to-emerald-600`
  - 文字: "高匹配度" (白色, 无边框, rounded-full)

### 表格设计系统

**表格头部**:
- 背景: `bg-gray-50/50`
- 边框: `border-b border-gray-200/50`
- 悬停: `hover:bg-gray-50/50`

**列定义**:
1. 序号 (w-20): 收藏图标 + 排名徽章
2. 代币 (w-32): 头像 + 符号
3. 名称 (w-32): 代币全名
4. 描述: 可展开的描述文本
5. 流通量 (w-24): 美元图标 + 数值
6. 总量 (w-24): 美元图标 + 数值
7. 官网 (w-20): 外链图标按钮
8. 白皮书 (w-20): 文档图标按钮
9. 推特 (w-20): Twitter图标按钮
10. 操作 (w-24): 深度研究按钮

**表格行设计**:
- 边框: `border-b border-gray-200/30`
- 悬停: `hover:bg-gray-50/30`
- 过渡: `transition-all duration-200`

**特殊元素**:
- **收藏图标**: Star图标，可切换填充状态
- **头像容器**: `w-10 h-10 bg-gradient-to-br from-teal-500 to-blue-600 rounded-full`
- **排名徽章**: `w-8 h-8 rounded-full border-gray-300`
- **展开按钮**: ChevronUp/ChevronDown图标
- **链接按钮**: 8x8圆形，各有不同的悬停颜色
- **研究按钮**: 渐变紫色背景，包含图标和文字

### 动画系统
**页面入场动画**:
- 搜索区域: `initial={{ opacity: 0, y: -20 }}`
- 结果卡片: `initial={{ opacity: 0, y: 20 }}, delay: 0.2`
- 表格内容: `initial={{ opacity: 0, y: 20 }}, delay: 0.4`

**表格行动画**:
- 每行独立动画: `initial={{ opacity: 0, x: -20 }}`
- 序列延迟: `delay: 0.6 + index * 0.1`

## ResearchReport组件规格

### 弹窗架构
使用Dialog组件构建模态弹窗，包含：
- 触发器：深度研究按钮
- 内容区域：研究报告详情
- 关闭机制：ESC键和点击外部区域

### 报告内容结构
- **头部**: 代币信息和关闭按钮
- **主体**: AI生成的研究分析内容
- **底部**: 免责声明和操作按钮

## 色彩设计系统

### 主色调
- **主渐变**: `from-indigo-500 to-purple-600`
- **悬停渐变**: `from-indigo-600 to-purple-700`
- **文字渐变**: `from-indigo-600 via-purple-600 to-blue-600`

### 辅助色彩
- **成功色**: `from-green-500 to-emerald-600`
- **背景色**: 白色半透明 `bg-white/80`
- **边框色**: `border-gray-200/50`
- **文字色**: 
  - 主文字: `text-gray-800`
  - 次文字: `text-gray-600`
  - 占位符: `text-gray-500`

### 交互色彩
- **蓝色系**: 官网链接 (`hover:text-blue-600`)
- **绿色系**: 白皮书链接 (`hover:text-green-600`)
- **天蓝色系**: 推特链接 (`hover:text-sky-600`)
- **紫色系**: 展开按钮和主要交互 (`text-purple-600`)

## 响应式设计

### 断点系统
- **移动端**: 默认样式
- **中等屏幕**: `md:` 前缀
- **大屏幕**: 自动适应

### 响应式调整
- 主标题: `text-5xl md:text-6xl`
- 最大宽度: `max-w-4xl` 和 `max-w-7xl`
- 间距: 使用space-y-*进行垂直间距

## 动画效果规格

### Motion动画库配置
- **持续时间**: 0.5s - 1.0s
- **缓动函数**: 默认easing
- **延迟模式**: 序列动画使用递增延迟

### 页面切换动画
- **模式**: `AnimatePresence mode="wait"`
- **退出动画**: `exit={{ opacity: 0, y: -20 }}`
- **进入动画**: `initial={{ opacity: 0, y: 20 }}`

### 微交互动画
- **按钮悬停**: scale和shadow变化
- **卡片悬停**: shadow-xl效果
- **输入框焦点**: border-color和shadow变化

## 数据结构定义

### TokenResult接口
```typescript
interface TokenResult {
  id: string;           // 唯一标识符
  rank: number;         // 排名
  symbol: string;       // 代币符号
  name: string;         // 代币全名
  description: string;  // 描述信息
  circulatingSupply: string; // 流通量
  totalSupply: string;  // 总供应量
  logo: string;         // 头像字符
  whitepaperUrl: string; // 白皮书链接
  twitterUrl: string;   // 推特链接
  website: string;      // 官网链接
}
```

## 性能优化策略

### 图片处理
- 使用ImageWithFallback组件
- 支持figma:asset路径
- 自动fallback机制

### 动画优化
- 使用transform进行GPU加速
- 合理使用will-change属性
- 避免布局抖动

### 代码分割
- 组件级别懒加载
- 路由级别代码分割
- 第三方库按需加载

## 可访问性设计

### 键盘导航
- Tab键顺序合理
- 回车键触发搜索
- ESC键关闭弹窗

### 语义化HTML
- 正确使用header、main、section等标签
- 表格使用thead、tbody结构
- 按钮和链接语义明确

### 颜色对比度
- 确保文字颜色符合WCAG标准
- 提供足够的对比度
- 不依赖颜色传达信息

## 技术实现细节

### 状态管理
- 使用React useState进行本地状态管理
- 搜索状态、结果状态、UI状态分离

### 事件处理
- 防抖优化搜索输入
- 合理的事件委托
- 避免内存泄漏

### 类型安全
- 严格的TypeScript类型定义
- Props接口完整定义
- 运行时类型检查

这份技术规格文档包含了重现整个网站所需的所有关键信息，从布局结构到动画细节，从色彩系统到交互逻辑，确保能够精确复制出相同的用户体验。