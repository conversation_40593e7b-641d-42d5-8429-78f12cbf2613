import React, { useState } from 'react';
import { Header } from './components/Header';
import { SearchSection } from './components/SearchSection';
import { SearchResults } from './components/SearchResults';
import { motion, AnimatePresence } from 'motion/react';

interface TokenResult {
  id: string;
  rank: number;
  symbol: string;
  name: string;
  description: string;
  circulatingSupply: string;
  totalSupply: string;
  logo: string;
  whitepaperUrl: string;
  twitterUrl: string;
  website: string;
}

export default function App() {
  const [hasSearched, setHasSearched] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [results, setResults] = useState<TokenResult[]>([]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    setHasSearched(true);
    
    // 模拟搜索结果 - 添加更多数据展示表格效果
    const mockResults: TokenResult[] = [
      {
        id: '1',
        rank: 1,
        symbol: 'MATIC',
        name: 'Polygon',
        description: '以太坊扩容解决方案，提供更快更便宜的交易体验，支持多种DeFi应用和NFT生态系统',
        circulatingSupply: '8.00B',
        totalSupply: '10.00B',
        logo: 'M',
        whitepaperUrl: 'https://polygon.technology/papers/pol-whitepaper',
        twitterUrl: 'https://twitter.com/0xPolygon',
        website: 'https://polygon.technology'
      },
      {
        id: '2',
        rank: 2,
        symbol: 'LINK',
        name: 'Chainlink',
        description: '去中心化预言机网络，为智能合约提供可靠的现实世界数据，是DeFi生态系统的重要基础设施',
        circulatingSupply: '556.8M',
        totalSupply: '1.00B',
        logo: 'L',
        whitepaperUrl: 'https://link.smartcontract.com/whitepaper',
        twitterUrl: 'https://twitter.com/chainlink',
        website: 'https://chain.link'
      },
      {
        id: '3',
        rank: 3,
        symbol: 'UNI',
        name: 'Uniswap',
        description: '领先的去中心化交易协议，自动化做市商模式革新了代币交易方式，支持多链部署',
        circulatingSupply: '753.8M',
        totalSupply: '1.00B',
        logo: 'U',
        whitepaperUrl: 'https://uniswap.org/whitepaper-v3.pdf',
        twitterUrl: 'https://twitter.com/Uniswap',
        website: 'https://uniswap.org'
      }
    ];
    
    // 添加延迟模拟真实搜索
    setTimeout(() => {
      setResults(mockResults);
    }, 1000);
  };

  const handleNewSearch = () => {
    setHasSearched(false);
    setSearchQuery('');
    setResults([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-indigo-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-indigo-300/10 to-purple-300/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        <Header />
        
        <AnimatePresence mode="wait">
          {!hasSearched ? (
            <motion.div
              key="search"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              <SearchSection onSearch={handleSearch} />
            </motion.div>
          ) : (
            <motion.div
              key="results"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <SearchResults 
                query={searchQuery}
                results={results}
                onNewSearch={handleNewSearch}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}